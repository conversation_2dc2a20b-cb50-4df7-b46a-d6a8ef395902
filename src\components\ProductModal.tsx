'use client'

import { X, Upload, Package, Trash2, Save } from 'lucide-react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'

interface ProductModalProps {
  isOpen: boolean
  onClose: () => void
  product?: Product | null
}

export default function ProductModal({ isOpen, onClose, product }: ProductModalProps) {
  const { resolvedTheme } = useTheme()
  const [formData, setFormData] = useState({
    name: '',
    net_weight: '',
    price: '',
    retail_price: '',
    stock_quantity: '',
    category: '',
    image_url: '',
    image_public_id: ''
  })
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        net_weight: product.net_weight,
        price: product.price.toString(),
        retail_price: product.retail_price?.toString() || '',
        stock_quantity: product.stock_quantity.toString(),
        category: product.category,
        image_url: product.image_url || '',
        image_public_id: product.image_public_id || ''
      })
      setImagePreview(product.image_url || '')
    } else {
      setFormData({
        name: '',
        net_weight: '',
        price: '',
        retail_price: '',
        stock_quantity: '',
        category: '',
        image_url: '',
        image_public_id: ''
      })
      setImagePreview('')
    }
    setImageFile(null)
  }, [product, isOpen])

  // Add beforeunload event listener to detect unexpected page refreshes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (loading || uploading) {
        console.warn('⚠️ ProductModal: Page refresh detected during form submission!')
        e.preventDefault()
        e.returnValue = 'Are you sure you want to leave? Your changes may not be saved.'
        return 'Are you sure you want to leave? Your changes may not be saved.'
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [loading, uploading])

  const compressImage = (file: File): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate new dimensions (max 800x800)
        const maxSize = 800
        let { width, height } = img

        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width
            width = maxSize
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height
            height = maxSize
          }
        }

        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              })
              resolve(compressedFile)
            } else {
              resolve(file) // Fallback to original
            }
          },
          'image/jpeg',
          0.8 // 80% quality
        )
      }

      img.src = URL.createObjectURL(file)
    })
  }

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      try {
        // Compress image if it's larger than 1MB
        const finalFile = file.size > 1024 * 1024 ? await compressImage(file) : file

        setImageFile(finalFile)
        const reader = new FileReader()
        reader.onloadend = () => {
          setImagePreview(reader.result as string)
        }
        reader.readAsDataURL(finalFile)

        // Show compression info
        if (file.size > finalFile.size) {
          console.log(`📦 Image compressed: ${(file.size / 1024 / 1024).toFixed(2)}MB → ${(finalFile.size / 1024 / 1024).toFixed(2)}MB`)
        }
      } catch (error) {
        console.error('Error processing image:', error)
        // Fallback to original file
        setImageFile(file)
        const reader = new FileReader()
        reader.onloadend = () => {
          setImagePreview(reader.result as string)
        }
        reader.readAsDataURL(file)
      }
    }
  }

  const handleRemoveImage = async () => {
    // Delete from Cloudinary if there's a public_id
    if (formData.image_public_id) {
      try {
        const response = await fetch(`/api/upload?public_id=${encodeURIComponent(formData.image_public_id)}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          console.log(`Successfully deleted product image: ${formData.image_public_id}`)
        } else {
          console.error('Failed to delete product image from Cloudinary')
        }
      } catch (error) {
        console.error('Error deleting product image:', error)
      }
    }

    // Clear the image data
    setFormData(prev => ({
      ...prev,
      image_url: '',
      image_public_id: ''
    }))
    setImagePreview('')
    setImageFile(null)
  }

  const uploadImage = async (): Promise<{ url: string; public_id: string }> => {
    // If no new image file, return existing image data
    if (!imageFile) {
      return {
        url: formData.image_url || '',
        public_id: formData.image_public_id || ''
      }
    }

    setUploading(true)
    try {
      console.log(`📤 Uploading image: ${imageFile.name} (${(imageFile.size / 1024 / 1024).toFixed(2)}MB)`)

      const uploadFormData = new FormData()
      uploadFormData.append('file', imageFile)

      // Include old public_id for automatic cleanup
      if (formData.image_public_id) {
        uploadFormData.append('old_public_id', formData.image_public_id)
      }

      // Create AbortController for timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
      }, 45000) // 45 second timeout

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: uploadFormData,
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`)
      }

      const data = await response.json()

      if (!data.url || !data.public_id) {
        throw new Error('Invalid upload response: missing url or public_id')
      }

      console.log('✅ Upload successful:', data.url)
      return { url: data.url, public_id: data.public_id }
    } catch (error) {
      console.error('Error uploading image:', error)

      // Handle specific error types
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Upload timeout - please try with a smaller image')
        } else if (error.message.includes('timeout')) {
          throw new Error('Upload timeout - please check your internet connection and try again')
        }
      }

      // Re-throw with more context
      throw new Error(`Image upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setUploading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('🔄 ProductModal: Form submission started')

    // CRITICAL: Prevent default form submission behavior to avoid page refresh
    e.preventDefault()
    e.stopPropagation()

    // Prevent multiple submissions
    if (loading || uploading) {
      console.log('⚠️ ProductModal: Submission blocked - already loading')
      return
    }

    setLoading(true)
    console.log('🔄 ProductModal: Loading state set to true')

    try {
      // Upload image if there's a new one - with better error handling
      let imageData
      try {
        console.log('🔄 ProductModal: Starting image upload')
        imageData = await uploadImage()
        console.log('✅ ProductModal: Image upload successful', imageData)
      } catch (uploadError) {
        console.error('❌ ProductModal: Image upload failed:', uploadError)

        // Provide more specific error messages
        let errorMessage = 'Failed to upload image. Please try again.'
        if (uploadError instanceof Error) {
          if (uploadError.message.includes('configuration error')) {
            errorMessage = 'Server configuration error. Please contact support.'
          } else if (uploadError.message.includes('file type')) {
            errorMessage = 'Invalid file type. Please use JPEG, PNG, or WebP images.'
          } else if (uploadError.message.includes('file size')) {
            errorMessage = 'File too large. Please use an image smaller than 5MB.'
          } else if (uploadError.message.includes('500')) {
            errorMessage = 'Server error during upload. Please check your internet connection and try again.'
          }
        }

        alert(errorMessage)
        return
      }

      const productData = {
        ...formData,
        image_url: imageData.url,
        image_public_id: imageData.public_id,
        price: parseFloat(formData.price),
        retail_price: formData.retail_price ? parseFloat(formData.retail_price) : null,
        stock_quantity: parseInt(formData.stock_quantity),
        // Include old image public_id for cleanup during update
        ...(product && { old_image_public_id: product.image_public_id })
      }

      const url = product ? `/api/products/${product.id}` : '/api/products'
      const method = product ? 'PUT' : 'POST'

      console.log(`🔄 ProductModal: Sending ${method} request to ${url}`)

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      })

      console.log('📡 ProductModal: API response received', response.status, response.statusText)

      if (response.ok) {
        console.log('✅ ProductModal: Product saved successfully, closing modal')
        // Success: Close modal and let parent handle refresh
        onClose()
        // The parent component (ProductsSection) will handle refreshing the products list
        // through the handleModalClose callback which calls fetchProducts() and onStatsUpdate()
      } else {
        const errorData = await response.json()
        console.error('❌ ProductModal: API error:', errorData)
        alert(errorData.error || 'Failed to save product')
      }
    } catch (error) {
      console.error('❌ ProductModal: Unexpected error:', error)
      alert('Failed to save product. Please try again.')
    } finally {
      setLoading(false)
      console.log('🔄 ProductModal: Loading state set to false')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4">
      <div
        className="rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-2xl"
        style={{
          backgroundColor: '#1e293b',
          border: '1px solid #334155'
        }}
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-600">
          <h2 className="text-xl font-bold text-white">
            {product ? 'Edit Product in List' : 'Add Product to List'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-white hover:text-gray-300 hover:bg-gray-700 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form
          onSubmit={handleSubmit}
          className="space-y-6"
          noValidate
          autoComplete="off"
          onKeyDown={(e) => {
            // Prevent Enter key from submitting form unless it's the submit button
            if (e.key === 'Enter' && e.target !== e.currentTarget) {
              const target = e.target as HTMLElement
              if (target.tagName !== 'BUTTON' || target.getAttribute('type') !== 'submit') {
                e.preventDefault()
              }
            }
          }}
        >
          {/* Image Upload Section */}
          <div>
            <label className="block text-sm font-medium mb-3 text-white">
              Product Image
            </label>
            <div className="flex items-center gap-12">
              {/* Image Preview - Left Side */}
              <div
                className="w-80 h-48 border-2 border-dashed rounded-lg flex items-center justify-center flex-shrink-0"
                style={{ borderColor: '#6b7280' }}
              >
                {imagePreview ? (
                  <Image
                    src={imagePreview}
                    alt="Preview"
                    width={320}
                    height={192}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <Package className="h-24 w-24 text-gray-400" />
                )}
              </div>

              {/* Action Buttons - Right Side (25% width) */}
              <div className="flex flex-col gap-3" style={{ width: '25%' }}>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="flex items-center px-4 py-2 rounded-md cursor-pointer transition-colors"
                  style={{
                    backgroundColor: '#555555',
                    color: '#ffffff'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#666666'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#555555'
                  }}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Choose Image
                </label>

                {(imagePreview || formData.image_url) && (
                  <button
                    type="button"
                    onClick={handleRemoveImage}
                    className="flex items-center px-4 py-2 rounded-md cursor-pointer transition-colors"
                    style={{
                      backgroundColor: '#EF4444',
                      color: '#ffffff'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#DC2626'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = '#EF4444'
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove
                  </button>
                )}
              </div>
            </div>
            <p className="text-xs text-gray-400 mt-3">
              💡 Tip: For faster uploads, use images smaller than 2MB. Large images will be automatically compressed.
            </p>
          </div>

          {/* Product Name - Full Width */}
          <div>
            <label className="block text-sm font-medium mb-1 text-white">
              Product Name *
            </label>
            <input
              id="product-name"
              name="name"
              type="text"
              required
              placeholder="Enter product name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400"
              style={{
                borderColor: '#6b7280',
                backgroundColor: '#333333',
                color: '#ffffff'
              }}
              autoComplete="off"
            />
          </div>

          {/* Two Column Layout for Net Weight and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Net Weight */}
            <div>
              <label className="block text-sm font-medium mb-1 text-white">
                Net Weight *
              </label>
              <input
                id="product-net-weight"
                name="net_weight"
                type="text"
                required
                placeholder="e.g., 100g, 1L, 250ml"
                value={formData.net_weight}
                onChange={(e) => setFormData({ ...formData, net_weight: e.target.value })}
                className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400"
                style={{
                  borderColor: '#6b7280',
                  backgroundColor: '#333333',
                  color: '#ffffff'
                }}
                autoComplete="off"
              />
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium mb-1 text-white">
                Category *
              </label>
              <select
                id="product-category"
                name="category"
                required
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                style={{
                  borderColor: '#6b7280',
                  backgroundColor: '#333333',
                  color: '#ffffff'
                }}
              >
                <option value="" style={{ backgroundColor: '#333333', color: '#9ca3af' }}>
                  Select a category
                </option>
                {PRODUCT_CATEGORIES.map((category) => (
                  <option key={category} value={category} style={{ backgroundColor: '#333333', color: '#ffffff' }}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Three Column Layout for Prices and Stock */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Unit Price */}
            <div>
              <label htmlFor="product-price" className="block text-sm font-medium mb-1 text-white">
                Unit Price (₱) *
              </label>
              <input
                id="product-price"
                name="price"
                type="number"
                step="0.01"
                min="0"
                required
                placeholder="0.00"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400"
                style={{
                  borderColor: '#6b7280',
                  backgroundColor: '#333333',
                  color: '#ffffff'
                }}
                autoComplete="off"
              />
            </div>

            {/* Retail Price */}
            <div>
              <label htmlFor="product-retail-price" className="block text-sm font-medium mb-1 text-white">
                Retail Price (₱)
              </label>
              <input
                id="product-retail-price"
                name="retail_price"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                value={formData.retail_price}
                onChange={(e) => setFormData({ ...formData, retail_price: e.target.value })}
                className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400"
                style={{
                  borderColor: '#6b7280',
                  backgroundColor: '#333333',
                  color: '#ffffff'
                }}
                autoComplete="off"
              />
              <p className="text-xs mt-1 text-gray-400">
                Optional: Selling price to customers
              </p>
            </div>

            {/* Stock Quantity */}
            <div>
              <label htmlFor="product-stock" className="block text-sm font-medium mb-1 text-white">
                Stock Quantity *
              </label>
              <input
                id="product-stock"
                name="stock_quantity"
                type="number"
                min="0"
                required
                placeholder="0"
                value={formData.stock_quantity}
                onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}
                className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400"
                style={{
                  borderColor: '#6b7280',
                  backgroundColor: '#333333',
                  color: '#ffffff'
                }}
                autoComplete="off"
              />
              <p className="text-xs mt-1 text-gray-400">
                Available quantity in stock
              </p>
            </div>
          </div>



          {/* Submit Buttons */}
          <div className="flex space-x-3 pt-6 border-t border-gray-600">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border rounded-md transition-colors text-white"
              style={{
                borderColor: '#6b7280',
                backgroundColor: '#444444'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#555555'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#444444'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || uploading}
              className="flex-1 px-4 py-3 rounded-md transition-colors flex items-center justify-center text-white font-medium"
              style={{
                backgroundColor: '#10B981'
              }}
              onMouseEnter={(e) => {
                if (!loading && !uploading) {
                  e.currentTarget.style.backgroundColor = '#059669'
                }
              }}
              onMouseLeave={(e) => {
                if (!loading && !uploading) {
                  e.currentTarget.style.backgroundColor = '#10B981'
                }
              }}
              onClick={(e) => {
                // Additional safeguard to prevent any default behavior
                if (loading || uploading) {
                  e.preventDefault()
                  e.stopPropagation()
                }
              }}
            >
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading Image...
                </>
              ) : loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving Product...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {product ? 'Update in List' : 'Add to List'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
