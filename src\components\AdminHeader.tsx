'use client'

import {
  Search, Home, Package, Image, Moon, Sun, LogOut, User, CreditCard,
  TrendingUp, AlertCircle, CheckCircle, Clock, Filter, Star,
  ShoppingCart, Eye, Edit, Trash2, Plus, X, ChevronDown
} from 'lucide-react'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import { useState, useEffect, useCallback, useRef } from 'react'

import { useAuth } from '@/contexts/AuthContext'
import type { Product } from '@/types'

interface AdminHeaderProps {
  activeSection: string
  setActiveSection: (section: string) => void
}

// Product search and display interfaces
interface ProductSearchResult {
  id: string
  name: string
  price: number
  retail_price?: number
  stock_quantity: number
  category: string
  image_url?: string
  created_at: string
}

interface HeaderStats {
  totalProducts: number
  lowStockCount: number
  recentProducts: ProductSearchResult[]
  topCategories: { name: string; count: number }[]
  lastUpdated: Date
}

export default function AdminHeader({ activeSection, setActiveSection }: AdminHeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const { setTheme, resolvedTheme } = useTheme()
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { user, logout } = useAuth()

  // Enhanced header state
  const [headerStats, setHeaderStats] = useState<HeaderStats>({
    totalProducts: 0,
    lowStockCount: 0,
    recentProducts: [],
    topCategories: [],
    lastUpdated: new Date()
  })
  const [searchResults, setSearchResults] = useState<ProductSearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const searchRef = useRef<HTMLDivElement>(null)

  // Fetch header statistics and recent products
  const fetchHeaderStats = useCallback(async () => {
    try {
      setError(null)
      console.log('🔄 Header: Fetching product statistics...')

      const response = await fetch('/api/products')
      if (!response.ok) throw new Error('Failed to fetch products')

      const data = await response.json()
      const products: Product[] = data.data?.products || []

      // Calculate statistics
      const totalProducts = products.length
      const lowStockCount = products.filter(p => p.stock_quantity <= 10).length
      const recentProducts = products
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5)
        .map(p => ({
          id: p.id,
          name: p.name,
          price: p.price,
          retail_price: p.retail_price,
          stock_quantity: p.stock_quantity,
          category: p.category,
          image_url: p.image_url,
          created_at: p.created_at
        }))

      // Calculate top categories
      const categoryCount = products.reduce((acc, product) => {
        acc[product.category] = (acc[product.category] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const topCategories = Object.entries(categoryCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([name, count]) => ({ name, count }))

      setHeaderStats({
        totalProducts,
        lowStockCount,
        recentProducts,
        topCategories,
        lastUpdated: new Date()
      })

      console.log('✅ Header: Statistics updated successfully', {
        totalProducts,
        lowStockCount,
        recentProducts: recentProducts.length,
        topCategories: topCategories.length
      })

    } catch (error) {
      console.error('❌ Header: Error fetching statistics:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch data')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Search products in real-time
  const searchProducts = useCallback(async (query: string) => {
    if (!query.trim() || query.length < 2) {
      setSearchResults([])
      setShowSearchResults(false)
      return
    }

    setIsSearching(true)
    try {
      const response = await fetch(`/api/products?search=${encodeURIComponent(query)}&limit=8`)
      if (!response.ok) throw new Error('Search failed')

      const data = await response.json()
      const products: Product[] = data.data?.products || []

      const results = products.map(p => ({
        id: p.id,
        name: p.name,
        price: p.price,
        retail_price: p.retail_price,
        stock_quantity: p.stock_quantity,
        category: p.category,
        image_url: p.image_url,
        created_at: p.created_at
      }))

      setSearchResults(results)
      setShowSearchResults(true)

    } catch (error) {
      console.error('❌ Header: Search error:', error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }, [])

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  // Fetch data on mount
  useEffect(() => {
    if (mounted) {
      fetchHeaderStats()
    }
  }, [mounted, fetchHeaderStats])

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchProducts(searchQuery)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery, searchProducts])

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearchResults(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])



  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Home Dashboard',
      icon: Home,
      tooltip: 'Dashboard Overview'
    },
    {
      id: 'products',
      label: 'Product Lists',
      icon: Package,
      tooltip: 'Manage Products'
    },
    {
      id: 'debts',
      label: 'Debt Management',
      icon: CreditCard,
      tooltip: 'Customer Debt (Utang) Management'
    },
    {
      id: 'family-gallery',
      label: 'Family Gallery',
      icon: Image,
      tooltip: 'Family Photos & Memories'
    },
  ]

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Navigate to products section with search query
      setActiveSection('products')
      // The search will be handled by the searchProducts function via useEffect
    }
  }

  const handleProductSelect = (product: ProductSearchResult) => {
    setActiveSection('products')
    setShowSearchResults(false)
    setSearchQuery('')
    // Could emit an event or use context to highlight the selected product
  }

  const toggleTheme = () => {
    if (!mounted) return

    // Manual DOM manipulation for immediate visual feedback
    const html = document.documentElement
    const isDark = resolvedTheme === 'dark'

    if (isDark) {
      html.classList.remove('dark')
      setTheme('light')
    } else {
      html.classList.add('dark')
      setTheme('dark')
    }
  }

  const handleLogout = () => {
    logout()
    window.location.href = '/login'
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300" style={{
      backgroundColor: resolvedTheme === 'dark' ? '#111827' : '#ffffff',
      borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'
    }}>
      <div className="grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4">
        
        {/* Left Section - Logo & Search (Fixed Width) */}
        <div className="flex items-center space-x-3 w-auto">
          {/* Revantad Logo */}
          <Link
            href="/landing"
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0"
            title="Return to Front Page"
          >
            <div className="w-10 h-10 hero-gradient rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-lg">R</span>
            </div>
            <span className="text-xl font-bold text-gradient hidden sm:block">Revantad</span>
          </Link>

          {/* Enhanced Search Bar with Real-time Results */}
          <div ref={searchRef} className="relative w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48">
            <form onSubmit={handleSearch}>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  id="header-search"
                  name="search"
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => searchQuery.length >= 2 && setShowSearchResults(true)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-slate-700 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-slate-600 transition-all duration-200"
                  autoComplete="off"
                />
                {isSearching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>
            </form>

            {/* Search Results Dropdown */}
            {showSearchResults && searchResults.length > 0 && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-slate-800 rounded-lg shadow-xl border border-gray-200 dark:border-slate-700 z-50 max-h-96 overflow-y-auto">
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1 mb-1">
                    Found {searchResults.length} product{searchResults.length !== 1 ? 's' : ''}
                  </div>
                  {searchResults.map((product) => (
                    <button
                      key={product.id}
                      onClick={() => handleProductSelect(product)}
                      className="w-full text-left p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-200 group"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 dark:bg-slate-600 rounded-lg flex items-center justify-center flex-shrink-0">
                          {product.image_url ? (
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          ) : (
                            <Package className="w-4 h-4 text-gray-400" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {product.name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-2">
                            <span>₱{product.retail_price || product.price}</span>
                            <span>•</span>
                            <span className={`${product.stock_quantity <= 10 ? 'text-red-500' : 'text-green-500'}`}>
                              {product.stock_quantity} in stock
                            </span>
                          </div>
                        </div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          {product.category}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>

                {/* Quick Actions Footer */}
                <div className="border-t border-gray-200 dark:border-slate-700 p-2">
                  <button
                    onClick={() => {
                      setActiveSection('products')
                      setShowSearchResults(false)
                    }}
                    className="w-full text-center py-2 text-sm text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors duration-200"
                  >
                    View all products →
                  </button>
                </div>
              </div>
            )}

            {/* No Results */}
            {showSearchResults && searchResults.length === 0 && searchQuery.length >= 2 && !isSearching && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-slate-800 rounded-lg shadow-xl border border-gray-200 dark:border-slate-700 z-50">
                <div className="p-4 text-center">
                  <Search className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    No products found for "{searchQuery}"
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Center Section - Navigation Icons (Facebook-style) */}
        <div className="hidden sm:flex items-center justify-center">
          <div className="flex items-center space-x-3 md:space-x-4 lg:space-x-5">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const isActive = activeSection === item.id

              return (
                <button
                  key={item.id}
                  onClick={() => {
                    setActiveSection(item.id)
                  }}
                  className={`relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-300 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.08] hover:shadow-lg`}
                  style={{
                    background: isActive
                      ? (resolvedTheme === 'dark'
                          ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.25) 0%, rgba(16, 185, 129, 0.2) 100%)'
                          : 'linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(16, 185, 129, 0.1) 100%)')
                      : 'transparent',
                    color: isActive
                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                      : (resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'),
                    boxShadow: isActive
                      ? (resolvedTheme === 'dark'
                          ? '0 4px 12px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                          : '0 4px 12px rgba(34, 197, 94, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.8)')
                      : 'none',
                    border: isActive
                      ? (resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.4)' : '1px solid rgba(34, 197, 94, 0.3)')
                      : '1px solid transparent'
                  }}
                  title={item.tooltip}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.background = resolvedTheme === 'dark'
                        ? 'linear-gradient(135deg, rgba(71, 85, 105, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%)'
                        : 'linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)'
                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
                      e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                        ? '0 2px 8px rgba(0, 0, 0, 0.3)'
                        : '0 2px 8px rgba(0, 0, 0, 0.1)'
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.background = 'transparent'
                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'
                      e.currentTarget.style.boxShadow = 'none'
                    }
                  }}
                >
                  {/* Active indicator - Enhanced */}
                  {isActive && (
                    <>
                      <div
                        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-12 md:w-14 lg:w-16 h-1.5 rounded-full transition-all duration-300"
                        style={{
                          background: resolvedTheme === 'dark'
                            ? 'linear-gradient(90deg, #4ade80 0%, #22c55e 100%)'
                            : 'linear-gradient(90deg, #16a34a 0%, #22c55e 100%)',
                          boxShadow: '0 2px 6px rgba(34, 197, 94, 0.5)'
                        }}
                      />
                      {/* Glow effect */}
                      <div
                        className="absolute inset-0 rounded-xl opacity-20 transition-all duration-300"
                        style={{
                          background: resolvedTheme === 'dark'
                            ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 100%)'
                            : 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%)',
                          filter: 'blur(1px)'
                        }}
                      />
                    </>
                  )}

                  <Icon className="h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-300 group-hover:scale-110 relative z-10" />

                  {/* Real-time Data Indicators */}
                  {item.id === 'products' && headerStats.totalProducts > 0 && (
                    <div className="absolute -top-1 -right-1 bg-green-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                      {headerStats.totalProducts > 99 ? '99+' : headerStats.totalProducts}
                    </div>
                  )}

                  {item.id === 'products' && headerStats.lowStockCount > 0 && (
                    <div className="absolute -bottom-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full w-4 h-4 flex items-center justify-center animate-pulse">
                      !
                    </div>
                  )}

                  {isLoading && item.id === 'products' && (
                    <div className="absolute top-0 right-0 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  )}

                  {/* Enhanced Professional Tooltip with Real-time Data */}
                  <div className="absolute top-full mt-4 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-gray-800 text-white text-xs px-4 py-3 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-50 shadow-xl border border-gray-700">
                    <div className="font-semibold text-white">{item.label}</div>
                    <div className="text-gray-300 text-[10px] mt-1">{item.tooltip}</div>

                    {/* Real-time Stats in Tooltip */}
                    {item.id === 'products' && !isLoading && (
                      <div className="mt-2 pt-2 border-t border-gray-700">
                        <div className="flex items-center space-x-3 text-[10px]">
                          <div className="flex items-center space-x-1">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span>{headerStats.totalProducts} products</span>
                          </div>
                          {headerStats.lowStockCount > 0 && (
                            <div className="flex items-center space-x-1">
                              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                              <span>{headerStats.lowStockCount} low stock</span>
                            </div>
                          )}
                        </div>
                        {headerStats.topCategories.length > 0 && (
                          <div className="mt-1 text-[9px] text-gray-400">
                            Top: {headerStats.topCategories[0]?.name} ({headerStats.topCategories[0]?.count})
                          </div>
                        )}
                      </div>
                    )}

                    {/* Tooltip arrow */}
                    <div className="absolute -top-1.5 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-gray-900 dark:bg-gray-800 rotate-45 border-l border-t border-gray-700"></div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Mobile Navigation - Simplified */}
        <div className="sm:hidden flex items-center justify-center space-x-2">
          <button
            onClick={() => setActiveSection('dashboard')}
            className={`p-2 rounded-lg transition-colors ${
              activeSection === 'dashboard'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
            title="Dashboard"
          >
            <Home className="h-5 w-5" />
          </button>
          <button
            onClick={() => setActiveSection('products')}
            className={`p-2 rounded-lg transition-colors ${
              activeSection === 'products'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
            title="Products"
          >
            <Package className="h-5 w-5" />
          </button>
          <button
            onClick={() => setActiveSection('debts')}
            className={`p-2 rounded-lg transition-colors ${
              activeSection === 'debts'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
            title="Debts"
          >
            <CreditCard className="h-5 w-5" />
          </button>
        </div>

        {/* Right Section - Dark Mode & Profile */}
        <div className="flex items-center justify-end space-x-3">


          {/* Dark Mode Toggle */}
          <button
            onClick={toggleTheme}
            className="p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0"
            title={mounted ? `Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode (Current: ${resolvedTheme})` : 'Toggle theme'}
            disabled={!mounted}
          >
            {!mounted ? (
              <div className="h-5 w-5 bg-gray-400 rounded-full animate-pulse" />
            ) : resolvedTheme === 'dark' ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </button>

          {/* Profile Dropdown */}
          <div className="relative flex-shrink-0">
            <button
              onClick={() => setIsProfileOpen(!isProfileOpen)}
              className="flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow">
                <User className="h-4 w-4 text-white" />
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors">
                {user?.name || 'Admin'}
              </span>
            </button>

            {/* Dropdown Menu */}
            {isProfileOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1">
                <div className="px-4 py-2 border-b border-gray-200 dark:border-slate-700">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">{user?.name || 'Admin User'}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{user?.email || '<EMAIL>'}</p>
                </div>
                
                <button
                  onClick={() => setActiveSection('settings')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2"
                >
                  <User className="h-4 w-4" />
                  <span>Settings</span>
                </button>
                
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
