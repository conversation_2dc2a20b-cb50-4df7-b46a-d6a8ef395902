@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Revantad Store Brand Colors - Green and Mustard */
  --primary-green: #22c55e;
  --primary-green-dark: #16a34a;
  --secondary-mustard: #facc15;
  --secondary-mustard-dark: #eab308;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --primary-green: #15803d;
    --secondary-mustard: #a16207;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

html {
  scroll-behavior: smooth;
}

/* Professional Loading and Animation Keyframes */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

/* Revantad Store Custom Component Classes */
.btn-primary {
  @apply bg-green-500 hover:bg-green-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-secondary {
  @apply bg-yellow-400 hover:bg-yellow-500 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-outline {
  @apply border-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
}

.card {
  @apply bg-white dark:bg-slate-800 rounded-xl shadow-md border border-gray-100 dark:border-slate-700;
}

.hero-gradient {
  @apply bg-gradient-to-br from-green-500 via-green-600 to-yellow-400;
}

.text-gradient {
  @apply bg-gradient-to-r from-green-600 to-yellow-500 bg-clip-text text-transparent;
}

.glass-effect {
  @apply backdrop-blur-md bg-white/10 border border-white/20;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Enhanced Dashboard Animations */
.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.animate-pulse-glow {
  animation: pulseGlow 2s infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dashboard Card Enhancements */
.dashboard-card {
  @apply transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient Text Effects */
.text-gradient-blue {
  @apply bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent;
}

.text-gradient-green {
  @apply bg-gradient-to-r from-green-600 to-green-400 bg-clip-text text-transparent;
}

.text-gradient-yellow {
  @apply bg-gradient-to-r from-yellow-600 to-yellow-400 bg-clip-text text-transparent;
}

.text-gradient-red {
  @apply bg-gradient-to-r from-red-600 to-red-400 bg-clip-text text-transparent;
}

/* Custom Scrollbar for Dashboard */
.main-content-scroll::-webkit-scrollbar {
  width: 6px;
}

.main-content-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.main-content-scroll::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 197, 94, 0.5);
}

@keyframes moonGlow {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
    transform: scale(1);
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));
    transform: scale(1.05);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Moon Phase Enhancements */
.moon-phase-icon {
  transition: all 0.3s ease;
  cursor: pointer;
}

.moon-phase-icon:hover {
  animation: moonGlow 2s infinite;
}

.moon-phase-container {
  position: relative;
}

.calendar-day-cell {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.calendar-day-cell:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.calendar-day-cell:hover .moon-phase-icon {
  transform: scale(1.1);
}

.animate-fade-in-up {
  animation: fadeInUp 0.4s ease-out;
}

/* Bisaya-Tagalog Cultural Enhancements */
.bisaya-calendar {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.bisaya-text {
  letter-spacing: 0.025em;
  line-height: 1.6;
}

.cultural-accent {
  background: linear-gradient(135deg, #22c55e 0%, #facc15 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.hover-scale-102:hover {
  transform: scale(1.02);
}

.filipino-shadow {
  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.15), 0 2px 8px rgba(250, 204, 21, 0.1);
}

.filipino-shadow:hover {
  box-shadow: 0 8px 30px rgba(34, 197, 94, 0.2), 0 4px 12px rgba(250, 204, 21, 0.15);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.4);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.6);
}

/* Sidebar specific scrollbar */
.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}

/* Prevent text blurring on transforms */
.sidebar-nav-item {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Ensure crisp text rendering */
.crisp-text {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Ensure crisp image rendering */
.crisp-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
}

/* Profile picture specific optimizations - Facebook-like quality */
.profile-picture {
  image-rendering: auto;
  image-rendering: -webkit-optimize-contrast;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Ensure smooth scaling and high quality */
  -webkit-filter: none;
  filter: none;
  /* Prevent blur on transform */
  will-change: transform;
}

/* Customer debt profile pictures - extra crisp */
.customer-debt-profile {
  /* Force high quality rendering */
  image-rendering: auto !important;
  image-rendering: -webkit-optimize-contrast !important;
  image-rendering: optimize-contrast !important;

  /* Remove all blur effects */
  -webkit-filter: none !important;
  filter: none !important;
  -webkit-backdrop-filter: none !important;
  backdrop-filter: none !important;

  /* Prevent transform blur */
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
  -webkit-transform: translateZ(0) scale(1) !important;
  transform: translateZ(0) scale(1) !important;

  /* Force crisp edges */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;

  /* Prevent any scaling blur */
  will-change: auto !important;
  contain: layout style paint !important;

  /* Override any potential Tailwind or other CSS */
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure modal containers don't blur child images */
.fixed.inset-0 img,
.fixed.inset-0 .customer-debt-profile {
  -webkit-filter: none !important;
  filter: none !important;
  image-rendering: auto !important;
}

/* Enhanced backdrop blur for sticky elements */
.backdrop-blur-enhanced {
  backdrop-filter: blur(8px) saturate(180%);
  -webkit-backdrop-filter: blur(8px) saturate(180%);
}

/* Smooth scrolling for sidebar navigation */
.sidebar-nav-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
}

/* Enhanced scrollbar for navigation area - Professional & Larger */
.sidebar-nav-scroll {
  scrollbar-width: auto;
  scrollbar-color: rgba(34, 197, 94, 0.5) rgba(243, 244, 246, 0.4);
  overflow-y: auto !important;
  overflow-x: hidden;
}

.sidebar-nav-scroll::-webkit-scrollbar {
  width: 14px;
  background: rgba(243, 244, 246, 0.5);
  border-radius: 7px;
  display: block !important;
}

.sidebar-nav-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 7px;
  margin: 2px 0;
  border: 1px solid rgba(229, 231, 235, 0.6);
  min-height: 50px;
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.7) 0%, rgba(16, 185, 129, 0.6) 100%);
  border-radius: 7px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(34, 197, 94, 0.3);
  min-height: 30px;
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(16, 185, 129, 0.7) 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
  transform: scale(1.05);
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.4);
}

/* Dark theme enhanced scrollbar */
.dark .sidebar-nav-scroll {
  scrollbar-color: rgba(34, 197, 94, 0.5) rgba(51, 65, 85, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar {
  background: rgba(51, 65, 85, 0.6);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.6);
  border: 1px solid rgba(71, 85, 105, 0.7);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.7) 0%, rgba(16, 185, 129, 0.6) 100%);
  border: 2px solid rgba(30, 41, 59, 0.3);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
  border-color: rgba(30, 41, 59, 0.4);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 1) 0%, rgba(16, 185, 129, 0.9) 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.5);
}

/* Professional gradient overlays */
.gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), transparent);
  pointer-events: none;
}

.dark .gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.9), transparent);
}

.gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(249, 250, 251, 0.9), transparent);
  pointer-events: none;
}

.dark .gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(15, 23, 42, 0.9), transparent);
}

/* Professional Main Content Scrollbar */
.main-content-scroll {
  scroll-behavior: smooth;
  scrollbar-width: auto;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(243, 244, 246, 0.4);
}

.main-content-scroll::-webkit-scrollbar {
  width: 12px;
  background: rgba(243, 244, 246, 0.3);
  border-radius: 6px;
}

.main-content-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.3);
  border-radius: 6px;
  margin: 4px 0;
  border: 1px solid rgba(229, 231, 235, 0.4);
}

.main-content-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(37, 99, 235, 0.5) 100%);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  min-height: 30px;
}

.main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.7) 0%, rgba(37, 99, 235, 0.6) 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
  transform: scale(1.02);
}

.main-content-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.7) 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.4);
}

/* Dark theme for main content scrollbar */
.dark .main-content-scroll {
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(51, 65, 85, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar {
  background: rgba(51, 65, 85, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.5);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(37, 99, 235, 0.5) 100%);
  border: 1px solid rgba(30, 41, 59, 0.3);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.7) 100%);
  border-color: rgba(30, 41, 59, 0.4);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.8) 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.5);
}

/* Fade indicators for scrollable content */
.scroll-fade-top::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
  pointer-events: none;
  z-index: 5;
}

.dark .scroll-fade-top::before {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.8), transparent);
}

.scroll-fade-bottom::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(249, 250, 251, 0.8), transparent);
  pointer-events: none;
  z-index: 5;
}

.dark .scroll-fade-bottom::after {
  background: linear-gradient(to top, rgba(15, 23, 42, 0.8), transparent);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800;
}

/* Hover effects */
.hover-lift {
  @apply transition-transform duration-200 hover:-translate-y-1;
}

.hover-glow {
  @apply transition-shadow duration-200 hover:shadow-lg hover:shadow-green-500/25;
}

/* AI Assistant Animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.6);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes typing-dots {
  0%, 20% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  80%, 100% {
    transform: scale(1);
    opacity: 1;
  }
}

.ai-float {
  animation: float 3s ease-in-out infinite;
}

.ai-glow {
  animation: glow 2s ease-in-out infinite;
}

.ai-pulse-ring {
  animation: pulse-ring 1.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

.typing-dot {
  animation: typing-dots 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Smooth scrollbar for chat */
.chat-scroll::-webkit-scrollbar {
  width: 4px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 2px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}

/* Professional Sukli Border Animation */
@keyframes border-travel {
  0% {
    border-image: linear-gradient(90deg, #3b82f6, transparent, transparent, transparent) 1;
  }
  25% {
    border-image: linear-gradient(180deg, transparent, #3b82f6, transparent, transparent) 1;
  }
  50% {
    border-image: linear-gradient(270deg, transparent, transparent, #3b82f6, transparent) 1;
  }
  75% {
    border-image: linear-gradient(0deg, transparent, transparent, transparent, #3b82f6) 1;
  }
  100% {
    border-image: linear-gradient(90deg, #3b82f6, transparent, transparent, transparent) 1;
  }
}

.sukli-border-animation {
  position: relative;
  border: 2px solid transparent;
  border-radius: 0.5rem;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(90deg, transparent, transparent, transparent, transparent) border-box;
  transition: all 0.3s ease;
}

.dark .sukli-border-animation {
  background: linear-gradient(#1e293b, #1e293b) padding-box,
              linear-gradient(90deg, transparent, transparent, transparent, transparent) border-box;
}

.sukli-border-animation:hover {
  animation: border-travel 2s linear infinite;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(90deg, #3b82f6, #3b82f6, #3b82f6, #3b82f6) border-box;
}

.dark .sukli-border-animation:hover {
  background: linear-gradient(#1e293b, #1e293b) padding-box,
              linear-gradient(90deg, #3b82f6, #3b82f6, #3b82f6, #3b82f6) border-box;
}

/* Enhanced Professional Sukli Animation */
.sukli-border-simple {
  position: relative;
  border: 2px solid transparent;
  border-radius: 0.375rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 197, 253, 0.05));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  will-change: transform, box-shadow;
}

.sukli-border-simple::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 0.5rem;
  padding: 2px;
  background: linear-gradient(90deg, transparent, transparent, transparent, transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

.sukli-border-simple::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 0.375rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sukli-border-simple:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25), 0 2px 6px rgba(59, 130, 246, 0.15);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
}

.sukli-border-simple:hover::before {
  opacity: 1;
  background: linear-gradient(90deg, #3b82f6, transparent, transparent, transparent);
  animation: border-travel-enhanced 4s linear infinite;
}

.sukli-border-simple:hover::after {
  opacity: 1;
}

.sukli-border-simple:active {
  transform: translateY(0) scale(0.98);
  transition: transform 0.1s ease;
}

@keyframes border-travel-enhanced {
  0% {
    background: linear-gradient(90deg, #3b82f6 0%, rgba(59, 130, 246, 0.8) 20%, transparent 40%, transparent 100%);
  }
  25% {
    background: linear-gradient(180deg, transparent 0%, transparent 60%, rgba(59, 130, 246, 0.8) 80%, #3b82f6 100%);
  }
  50% {
    background: linear-gradient(270deg, transparent 0%, transparent 60%, rgba(59, 130, 246, 0.8) 80%, #3b82f6 100%);
  }
  75% {
    background: linear-gradient(0deg, transparent 0%, transparent 60%, rgba(59, 130, 246, 0.8) 80%, #3b82f6 100%);
  }
  100% {
    background: linear-gradient(90deg, #3b82f6 0%, rgba(59, 130, 246, 0.8) 20%, transparent 40%, transparent 100%);
  }
}

/* Pulse effect for extra attention */
@keyframes sukli-pulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25), 0 2px 6px rgba(59, 130, 246, 0.15);
  }
  50% {
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4), 0 4px 12px rgba(59, 130, 246, 0.25);
  }
}

/* Shimmer effect for the text */
@keyframes sukli-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.sukli-border-simple:hover .sukli-text {
  background: linear-gradient(90deg, #3b82f6, #60a5fa, #3b82f6);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: sukli-shimmer 2s ease-in-out infinite;
}

/* Dark mode enhancements */
.dark .sukli-border-simple {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.05));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .sukli-border-simple:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 197, 253, 0.1));
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), 0 2px 6px rgba(59, 130, 246, 0.2);
}

/* Professional Animation Utility Classes */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slideInFromLeft {
  animation: slideInFromLeft 0.6s ease-out forwards;
}

.animate-slideInFromRight {
  animation: slideInFromRight 0.6s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out forwards;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Professional Card Hover Effects */
.card-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Professional Button Animations */
.btn-professional {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-professional::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-professional:hover::before {
  left: 100%;
}

.btn-professional:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
























